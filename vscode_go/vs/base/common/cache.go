/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import "sync"

// CacheResult represents a cache result with a promise and disposable
type CacheResult[T any] struct {
	Promise chan T
	dispose func()
}

// Dispose disposes the cache result
func (c *CacheResult[T]) Dispose() {
	if c.dispose != nil {
		c.dispose()
	}
}

// Cache represents a cache that can store and retrieve values
type Cache[T any] struct {
	result *CacheResult[T]
	task   func(CancellationToken) T
	mutex  sync.Mutex
}

// NewCache creates a new Cache with the given task function
func NewCache[T any](task func(CancellationToken) T) *Cache[T] {
	return &Cache[T]{
		task: task,
	}
}

// Get gets the cached result or creates a new one
func (c *Cache[T]) Get() *CacheResult[T] {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.result != nil {
		return c.result
	}

	cts := NewCancellationTokenSource(nil)
	promise := make(chan T, 1)

	// Execute task in goroutine
	go func() {
		result := c.task(cts.Token())
		promise <- result
		close(promise)
	}()

	c.result = &CacheResult[T]{
		Promise: promise,
		dispose: func() {
			c.mutex.Lock()
			defer c.mutex.Unlock()
			c.result = nil
			cts.Cancel()
			cts.Dispose(false)
		},
	}

	return c.result
}

// Identity returns the input value unchanged
func Identity[T any](t T) T {
	return t
}

// ICacheOptions represents cache options
type ICacheOptions[TArg any] struct {
	GetCacheKey func(TArg) interface{}
}

// LRUCachedFunction represents a function with LRU caching (last key/value only)
type LRUCachedFunction[TArg, TComputed any] struct {
	lastCache  *TComputed
	lastArgKey interface{}
	fn         func(TArg) TComputed
	computeKey func(TArg) interface{}
	mutex      sync.RWMutex
}

// NewLRUCachedFunction creates a new LRUCachedFunction with just a function
func NewLRUCachedFunction[TArg, TComputed any](fn func(TArg) TComputed) *LRUCachedFunction[TArg, TComputed] {
	return &LRUCachedFunction[TArg, TComputed]{
		fn:         fn,
		computeKey: func(arg TArg) interface{} { return arg },
	}
}

// NewLRUCachedFunctionWithOptions creates a new LRUCachedFunction with options
func NewLRUCachedFunctionWithOptions[TArg, TComputed any](options ICacheOptions[TArg], fn func(TArg) TComputed) *LRUCachedFunction[TArg, TComputed] {
	return &LRUCachedFunction[TArg, TComputed]{
		fn:         fn,
		computeKey: options.GetCacheKey,
	}
}

// Get gets the cached result for the given argument
func (l *LRUCachedFunction[TArg, TComputed]) Get(arg TArg) TComputed {
	key := l.computeKey(arg)

	l.mutex.RLock()
	if l.lastArgKey == key && l.lastCache != nil {
		result := *l.lastCache
		l.mutex.RUnlock()
		return result
	}
	l.mutex.RUnlock()

	l.mutex.Lock()
	defer l.mutex.Unlock()

	// Double-check after acquiring write lock
	if l.lastArgKey == key && l.lastCache != nil {
		return *l.lastCache
	}

	l.lastArgKey = key
	result := l.fn(arg)
	l.lastCache = &result
	return result
}

// CachedFunction represents a function with unbounded caching
type CachedFunction[TArg, TComputed any] struct {
	mapByArg   map[interface{}]TComputed
	mapByKey   map[interface{}]TComputed
	fn         func(TArg) TComputed
	computeKey func(TArg) interface{}
	mutex      sync.RWMutex
}

// NewCachedFunction creates a new CachedFunction with just a function
func NewCachedFunction[TArg, TComputed any](fn func(TArg) TComputed) *CachedFunction[TArg, TComputed] {
	return &CachedFunction[TArg, TComputed]{
		mapByArg:   make(map[interface{}]TComputed),
		mapByKey:   make(map[interface{}]TComputed),
		fn:         fn,
		computeKey: func(arg TArg) interface{} { return arg },
	}
}

// NewCachedFunctionWithOptions creates a new CachedFunction with options
func NewCachedFunctionWithOptions[TArg, TComputed any](options ICacheOptions[TArg], fn func(TArg) TComputed) *CachedFunction[TArg, TComputed] {
	return &CachedFunction[TArg, TComputed]{
		mapByArg:   make(map[interface{}]TComputed),
		mapByKey:   make(map[interface{}]TComputed),
		fn:         fn,
		computeKey: options.GetCacheKey,
	}
}

// CachedValues returns a copy of the cached values map
func (c *CachedFunction[TArg, TComputed]) CachedValues() map[interface{}]TComputed {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := make(map[interface{}]TComputed)
	for k, v := range c.mapByArg {
		result[k] = v
	}
	return result
}

// Get gets the cached result for the given argument
func (c *CachedFunction[TArg, TComputed]) Get(arg TArg) TComputed {
	key := c.computeKey(arg)

	c.mutex.RLock()
	if value, exists := c.mapByKey[key]; exists {
		c.mutex.RUnlock()
		return value
	}
	c.mutex.RUnlock()

	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Double-check after acquiring write lock
	if value, exists := c.mapByKey[key]; exists {
		return value
	}

	value := c.fn(arg)
	c.mapByArg[arg] = value
	c.mapByKey[key] = value
	return value
}
