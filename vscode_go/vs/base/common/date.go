/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/yudaprama/kawai-agent/vscode_go/vs"
)

const (
	minute = 60
	hour   = minute * 60
	day    = hour * 24
	week   = day * 7
	month  = day * 30
	year   = day * 365
)

// FromNow creates a localized difference of the time between now and the specified date
func FromNow(date interface{}, appendAgoLabel, useFullTimeWords, disallowNow bool) string {
	var dateTime time.Time

	switch d := date.(type) {
	case int64:
		dateTime = time.Unix(d/1000, (d%1000)*1000000) // Convert milliseconds to time
	case time.Time:
		dateTime = d
	default:
		return ""
	}

	seconds := int(math.Round(time.Since(dateTime).Seconds()))

	if seconds < -30 {
		futureTime := time.Now().Add(time.Duration(seconds) * time.Second)
		return vs.Localize("date.fromNow.in", "in {0}", FromNow(futureTime.UnixMilli(), false, useFullTimeWords, disallowNow))
	}

	if !disallowNow && seconds < 30 {
		return vs.Localize("date.fromNow.now", "now")
	}

	var value int
	if seconds < minute {
		value = seconds
		if appendAgoLabel {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.seconds.singular.ago.fullWord", "{0} second ago", value)
				} else {
					return vs.Localize("date.fromNow.seconds.singular.ago", "{0} sec ago", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.seconds.plural.ago.fullWord", "{0} seconds ago", value)
				} else {
					return vs.Localize("date.fromNow.seconds.plural.ago", "{0} secs ago", value)
				}
			}
		} else {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.seconds.singular.fullWord", "{0} second", value)
				} else {
					return vs.Localize("date.fromNow.seconds.singular", "{0} sec", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.seconds.plural.fullWord", "{0} seconds", value)
				} else {
					return vs.Localize("date.fromNow.seconds.plural", "{0} secs", value)
				}
			}
		}
	}

	if seconds < hour {
		value = seconds / minute
		if appendAgoLabel {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.minutes.singular.ago.fullWord", "{0} minute ago", value)
				} else {
					return vs.Localize("date.fromNow.minutes.singular.ago", "{0} min ago", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.minutes.plural.ago.fullWord", "{0} minutes ago", value)
				} else {
					return vs.Localize("date.fromNow.minutes.plural.ago", "{0} mins ago", value)
				}
			}
		} else {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.minutes.singular.fullWord", "{0} minute", value)
				} else {
					return vs.Localize("date.fromNow.minutes.singular", "{0} min", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.minutes.plural.fullWord", "{0} minutes", value)
				} else {
					return vs.Localize("date.fromNow.minutes.plural", "{0} mins", value)
				}
			}
		}
	}

	if seconds < day {
		value = seconds / hour
		if appendAgoLabel {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.hours.singular.ago.fullWord", "{0} hour ago", value)
				} else {
					return vs.Localize("date.fromNow.hours.singular.ago", "{0} hr ago", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.hours.plural.ago.fullWord", "{0} hours ago", value)
				} else {
					return vs.Localize("date.fromNow.hours.plural.ago", "{0} hrs ago", value)
				}
			}
		} else {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.hours.singular.fullWord", "{0} hour", value)
				} else {
					return vs.Localize("date.fromNow.hours.singular", "{0} hr", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.hours.plural.fullWord", "{0} hours", value)
				} else {
					return vs.Localize("date.fromNow.hours.plural", "{0} hrs", value)
				}
			}
		}
	}

	if seconds < week {
		value = seconds / day
		if appendAgoLabel {
			if value == 1 {
				return vs.Localize("date.fromNow.days.singular.ago", "{0} day ago", value)
			} else {
				return vs.Localize("date.fromNow.days.plural.ago", "{0} days ago", value)
			}
		} else {
			if value == 1 {
				return vs.Localize("date.fromNow.days.singular", "{0} day", value)
			} else {
				return vs.Localize("date.fromNow.days.plural", "{0} days", value)
			}
		}
	}

	if seconds < month {
		value = seconds / week
		if appendAgoLabel {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.weeks.singular.ago.fullWord", "{0} week ago", value)
				} else {
					return vs.Localize("date.fromNow.weeks.singular.ago", "{0} wk ago", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.weeks.plural.ago.fullWord", "{0} weeks ago", value)
				} else {
					return vs.Localize("date.fromNow.weeks.plural.ago", "{0} wks ago", value)
				}
			}
		} else {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.weeks.singular.fullWord", "{0} week", value)
				} else {
					return vs.Localize("date.fromNow.weeks.singular", "{0} wk", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.weeks.plural.fullWord", "{0} weeks", value)
				} else {
					return vs.Localize("date.fromNow.weeks.plural", "{0} wks", value)
				}
			}
		}
	}

	if seconds < year {
		value = seconds / month
		if appendAgoLabel {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.months.singular.ago.fullWord", "{0} month ago", value)
				} else {
					return vs.Localize("date.fromNow.months.singular.ago", "{0} mo ago", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.months.plural.ago.fullWord", "{0} months ago", value)
				} else {
					return vs.Localize("date.fromNow.months.plural.ago", "{0} mos ago", value)
				}
			}
		} else {
			if value == 1 {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.months.singular.fullWord", "{0} month", value)
				} else {
					return vs.Localize("date.fromNow.months.singular", "{0} mo", value)
				}
			} else {
				if useFullTimeWords {
					return vs.Localize("date.fromNow.months.plural.fullWord", "{0} months", value)
				} else {
					return vs.Localize("date.fromNow.months.plural", "{0} mos", value)
				}
			}
		}
	}

	value = seconds / year
	if appendAgoLabel {
		if value == 1 {
			if useFullTimeWords {
				return vs.Localize("date.fromNow.years.singular.ago.fullWord", "{0} year ago", value)
			} else {
				return vs.Localize("date.fromNow.years.singular.ago", "{0} yr ago", value)
			}
		} else {
			if useFullTimeWords {
				return vs.Localize("date.fromNow.years.plural.ago.fullWord", "{0} years ago", value)
			} else {
				return vs.Localize("date.fromNow.years.plural.ago", "{0} yrs ago", value)
			}
		}
	} else {
		if value == 1 {
			if useFullTimeWords {
				return vs.Localize("date.fromNow.years.singular.fullWord", "{0} year", value)
			} else {
				return vs.Localize("date.fromNow.years.singular", "{0} yr", value)
			}
		} else {
			if useFullTimeWords {
				return vs.Localize("date.fromNow.years.plural.fullWord", "{0} years", value)
			} else {
				return vs.Localize("date.fromNow.years.plural", "{0} yrs", value)
			}
		}
	}
}

// FromNowByDay creates a localized difference by day
func FromNowByDay(date interface{}, appendAgoLabel, useFullTimeWords bool) string {
	var dateTime time.Time

	switch d := date.(type) {
	case int64:
		dateTime = time.Unix(d/1000, (d%1000)*1000000) // Convert milliseconds to time
	case time.Time:
		dateTime = d
	default:
		return ""
	}

	now := time.Now()
	todayMidnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	yesterdayMidnight := todayMidnight.AddDate(0, 0, -1)

	if dateTime.After(todayMidnight) {
		return vs.Localize("today", "Today")
	}

	if dateTime.After(yesterdayMidnight) {
		return vs.Localize("yesterday", "Yesterday")
	}

	return FromNow(dateTime.UnixMilli(), appendAgoLabel, useFullTimeWords, false)
}

// GetDurationString gets a readable duration with intelligent/lossy precision
func GetDurationString(ms int, useFullTimeWords bool) string {
	seconds := math.Abs(float64(ms) / 1000)

	if seconds < 1 {
		if useFullTimeWords {
			return vs.Localize("duration.ms.full", "{0} milliseconds", ms)
		} else {
			return vs.Localize("duration.ms", "{0}ms", ms)
		}
	}

	if seconds < minute {
		roundedMs := int(math.Round(float64(ms)))
		value := float64(roundedMs) / 1000
		if useFullTimeWords {
			return vs.Localize("duration.s.full", "{0} seconds", value)
		} else {
			return vs.Localize("duration.s", "{0}s", value)
		}
	}

	if seconds < hour {
		value := int(math.Round(float64(ms) / (1000 * minute)))
		if useFullTimeWords {
			return vs.Localize("duration.m.full", "{0} minutes", value)
		} else {
			return vs.Localize("duration.m", "{0} mins", value)
		}
	}

	if seconds < day {
		value := int(math.Round(float64(ms) / (1000 * hour)))
		if useFullTimeWords {
			return vs.Localize("duration.h.full", "{0} hours", value)
		} else {
			return vs.Localize("duration.h", "{0} hrs", value)
		}
	}

	value := int(math.Round(float64(ms) / (1000 * day)))
	return vs.Localize("duration.d", "{0} days", value)
}

// ToLocalISOString converts a time to local ISO string format
func ToLocalISOString(date time.Time) string {
	year := date.Year()
	month := int(date.Month())
	day := date.Day()
	hour := date.Hour()
	minute := date.Minute()
	second := date.Second()
	millisecond := date.Nanosecond() / 1000000

	return fmt.Sprintf("%04d-%02d-%02dT%02d:%02d:%02d.%03dZ",
		year, month, day, hour, minute, second, millisecond)
}

// SafeIntl provides safe internationalization functions
var SafeIntl = struct {
	DateTimeFormat func(locales interface{}, options interface{}) *Lazy[interface{}]
	Collator       func(locales interface{}, options interface{}) *Lazy[interface{}]
	Segmenter      func(locales interface{}, options interface{}) *Lazy[interface{}]
	Locale         func(tag interface{}, options interface{}) *Lazy[interface{}]
	NumberFormat   func(locales interface{}, options interface{}) *Lazy[interface{}]
}{
	DateTimeFormat: func(locales interface{}, options interface{}) *Lazy[interface{}] {
		return NewLazy(func() interface{} {
			// In Go, we would use time.Format or a localization library
			// For now, return a simple formatter
			return func(date time.Time) string {
				return date.Format(time.RFC3339)
			}
		})
	},
	Collator: func(locales interface{}, options interface{}) *Lazy[interface{}] {
		return NewLazy(func() interface{} {
			// In Go, we would use golang.org/x/text/collate
			// For now, return a simple string comparison
			return func(a, b string) int {
				return strings.Compare(a, b)
			}
		})
	},
	Segmenter: func(locales interface{}, options interface{}) *Lazy[interface{}] {
		return NewLazy(func() interface{} {
			// In Go, we would use golang.org/x/text/unicode/norm
			// For now, return a simple segmenter
			return func(text string) []string {
				return strings.Fields(text)
			}
		})
	},
	Locale: func(tag interface{}, options interface{}) *Lazy[interface{}] {
		return NewLazy(func() interface{} {
			// In Go, we would use golang.org/x/text/language
			// For now, return the default language
			tagStr, ok := tag.(string)
			if !ok {
				return LANGUAGE_DEFAULT
			}
			return tagStr
		})
	},
	NumberFormat: func(locales interface{}, options interface{}) *Lazy[interface{}] {
		return NewLazy(func() interface{} {
			// In Go, we would use golang.org/x/text/number
			// For now, return a simple number formatter
			return func(num float64) string {
				return strconv.FormatFloat(num, 'f', -1, 64)
			}
		})
	},
}
