/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

// ISplice represents a splice operation on a sequence
type ISplice[T any] struct {
	Start       int   `json:"start"`
	DeleteCount int   `json:"deleteCount"`
	ToInsert    []T   `json:"toInsert"`
}

// ISpliceable represents a type that can be spliced
type ISpliceable[T any] interface {
	Splice(start int, deleteCount int, toInsert []T)
}

// ISequence represents a sequence with splice events
type ISequence[T any] interface {
	Elements() []T
	OnDidSplice() Event[ISplice[T]]
}

// Sequence implements ISequence and ISpliceable
type Sequence[T any] struct {
	elements      []T
	onDidSplice   *Emitter[ISplice[T]]
}

// NewSequence creates a new sequence
func NewSequence[T any]() *Sequence[T] {
	return &Sequence[T]{
		elements:    make([]T, 0),
		onDidSplice: NewEmitter[ISplice[T]](),
	}
}

// Elements returns the elements in the sequence
func (s *Sequence[T]) Elements() []T {
	return s.elements
}

// OnDidSplice returns the splice event
func (s *Sequence[T]) OnDidSplice() Event[ISplice[T]] {
	return s.onDidSplice
}

// Splice performs a splice operation on the sequence
func (s *Sequence[T]) Splice(start int, deleteCount int, toInsert []T) {
	// Perform the splice operation similar to JavaScript's Array.splice()
	if start < 0 {
		start = 0
	}
	if start > len(s.elements) {
		start = len(s.elements)
	}
	
	if deleteCount < 0 {
		deleteCount = 0
	}
	if start+deleteCount > len(s.elements) {
		deleteCount = len(s.elements) - start
	}

	// Create new slice with the spliced result
	newElements := make([]T, 0, len(s.elements)-deleteCount+len(toInsert))
	
	// Add elements before the splice point
	newElements = append(newElements, s.elements[:start]...)
	
	// Add the new elements
	newElements = append(newElements, toInsert...)
	
	// Add elements after the splice point
	newElements = append(newElements, s.elements[start+deleteCount:]...)
	
	// Update the elements
	s.elements = newElements
	
	// Fire the splice event
	s.onDidSplice.Fire(ISplice[T]{
		Start:       start,
		DeleteCount: deleteCount,
		ToInsert:    toInsert,
	})
}
