/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

// IStringDictionary represents a dictionary with string keys
type IStringDictionary[V any] map[string]V

// INumberDictionary represents a dictionary with number keys
type INumberDictionary[V any] map[int]V

// GroupBy groups the collection into a dictionary based on the provided group function
func GroupBy[K comparable, V any](data []V, groupFn func(V) K) map[K][]V {
	result := make(map[K][]V)
	for _, element := range data {
		key := groupFn(element)
		result[key] = append(result[key], element)
	}
	return result
}

// DiffSets computes the difference between two sets
func DiffSets[T comparable](before map[T]bool, after map[T]bool) (removed []T, added []T) {
	// Find removed items
	for item := range before {
		if !after[item] {
			removed = append(removed, item)
		}
	}

	// Find added items
	for item := range after {
		if !before[item] {
			added = append(added, item)
		}
	}

	return removed, added
}

// ForEach iterates over a map and calls the provided function for each key-value pair
func ForEach[K comparable, V any](m map[K]V, fn func(K, V)) {
	for key, value := range m {
		fn(key, value)
	}
}

// Keys returns all keys from a map
func Keys[K comparable, V any](m map[K]V) []K {
	keys := make([]K, 0, len(m))
	for key := range m {
		keys = append(keys, key)
	}
	return keys
}

// Values returns all values from a map
func Values[K comparable, V any](m map[K]V) []V {
	values := make([]V, 0, len(m))
	for _, value := range m {
		values = append(values, value)
	}
	return values
}

// SetWithKey is a Set implementation that uses a custom key function
type SetWithKey[T any] struct {
	m     map[interface{}]T
	toKey func(T) interface{}
}

// NewSetWithKey creates a new SetWithKey with the given values and key function
func NewSetWithKey[T any](values []T, toKey func(T) interface{}) *SetWithKey[T] {
	s := &SetWithKey[T]{
		m:     make(map[interface{}]T),
		toKey: toKey,
	}
	for _, value := range values {
		s.Add(value)
	}
	return s
}

// Size returns the number of elements in the set
func (s *SetWithKey[T]) Size() int {
	return len(s.m)
}

// Add adds a value to the set
func (s *SetWithKey[T]) Add(value T) *SetWithKey[T] {
	key := s.toKey(value)
	s.m[key] = value
	return s
}

// Delete removes a value from the set
func (s *SetWithKey[T]) Delete(value T) bool {
	key := s.toKey(value)
	_, exists := s.m[key]
	if exists {
		delete(s.m, key)
	}
	return exists
}

// Has checks if a value exists in the set
func (s *SetWithKey[T]) Has(value T) bool {
	key := s.toKey(value)
	_, exists := s.m[key]
	return exists
}

// Clear removes all elements from the set
func (s *SetWithKey[T]) Clear() {
	s.m = make(map[interface{}]T)
}

// ForEach calls the provided function for each value in the set
func (s *SetWithKey[T]) ForEach(callbackfn func(value T, value2 T, set *SetWithKey[T])) {
	for _, value := range s.m {
		callbackfn(value, value, s)
	}
}

// Values returns a slice of all values in the set
func (s *SetWithKey[T]) Values() []T {
	values := make([]T, 0, len(s.m))
	for _, value := range s.m {
		values = append(values, value)
	}
	return values
}
