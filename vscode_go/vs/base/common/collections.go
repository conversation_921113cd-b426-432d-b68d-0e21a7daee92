/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

// IStringDictionary represents a dictionary with string keys
type IStringDictionary[V any] map[string]V

// INumberDictionary represents a dictionary with number keys
type INumberDictionary[V any] map[int]V

// GroupBy groups the collection into a dictionary based on the provided group function
func GroupBy[K comparable, V any](data []V, groupFn func(V) K) map[K][]V {
	result := make(map[K][]V)
	for _, element := range data {
		key := groupFn(element)
		result[key] = append(result[key], element)
	}
	return result
}



// DiffSets computes the difference between two sets
func DiffSets[T comparable](before map[T]bool, after map[T]bool) (removed []T, added []T) {
	// Find removed items
	for item := range before {
		if !after[item] {
			removed = append(removed, item)
		}
	}

	// Find added items
	for item := range after {
		if !before[item] {
			added = append(added, item)
		}
	}

	return removed, added
}

// ForEach iterates over a map and calls the provided function for each key-value pair
func ForEach[K comparable, V any](m map[K]V, fn func(K, V)) {
	for key, value := range m {
		fn(key, value)
	}
}

// Keys returns all keys from a map
func Keys[K comparable, V any](m map[K]V) []K {
	keys := make([]K, 0, len(m))
	for key := range m {
		keys = append(keys, key)
	}
	return keys
}

// Values returns all values from a map
func Values[K comparable, V any](m map[K]V) []V {
	values := make([]V, 0, len(m))
	for _, value := range m {
		values = append(values, value)
	}
	return values
}

// URIRevive recreates a URI from UriComponents
func URIRevive(components map[string]any) URI {
	scheme, _ := components["scheme"].(string)
	authority, _ := components["authority"].(string)
	path, _ := components["path"].(string)
	query, _ := components["query"].(string)
	fragment, _ := components["fragment"].(string)

	return *NewURI(scheme, authority, path, query, fragment)
}
