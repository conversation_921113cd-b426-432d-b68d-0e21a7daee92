/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

// INavigator represents a navigator interface
type INavigator[T any] interface {
	Current() *T
	Previous() *T
	First() *T
	Last() *T
	Next() *T
}

// ArrayNavigator implements INavigator for arrays
type ArrayNavigator[T any] struct {
	items []T
	start int
	end   int
	index int
}

// NewArrayNavigator creates a new ArrayNavigator
func NewArrayNavigator[T any](items []T, start, end, index int) *ArrayNavigator[T] {
	if start < 0 {
		start = 0
	}
	if end > len(items) {
		end = len(items)
	}
	if index < start-1 {
		index = start - 1
	}
	
	return &ArrayNavigator[T]{
		items: items,
		start: start,
		end:   end,
		index: index,
	}
}

// Current returns the current item or nil if at boundaries
func (n *ArrayNavigator[T]) Current() *T {
	if n.index == n.start-1 || n.index == n.end {
		return nil
	}
	return &n.items[n.index]
}

// Next moves to the next item and returns it
func (n *ArrayNavigator[T]) Next() *T {
	if n.index+1 <= n.end {
		n.index = n.index + 1
	} else {
		n.index = n.end
	}
	return n.Current()
}

// Previous moves to the previous item and returns it
func (n *ArrayNavigator[T]) Previous() *T {
	if n.index-1 >= n.start-1 {
		n.index = n.index - 1
	} else {
		n.index = n.start - 1
	}
	return n.Current()
}

// First moves to the first item and returns it
func (n *ArrayNavigator[T]) First() *T {
	n.index = n.start
	return n.Current()
}

// Last moves to the last item and returns it
func (n *ArrayNavigator[T]) Last() *T {
	n.index = n.end - 1
	return n.Current()
}
