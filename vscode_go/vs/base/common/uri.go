/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
)

// UriComponents represents the components of a URI
type UriComponents struct {
	Scheme    string `json:"scheme"`
	Authority string `json:"authority"`
	Path      string `json:"path"`
	Query     string `json:"query"`
	Fragment  string `json:"fragment"`
}

// URI represents a Uniform Resource Identifier
type URI struct {
	UriComponents
}

// UriDto represents a URI data transfer object that can be serialized/deserialized
type UriDto[T any] struct {
	Data T             `json:"data"`
	URI  UriComponents `json:"uri,omitempty"`
}

var (
	schemePattern    = regexp.MustCompile(`^\w[\w\d+.-]*$`)
	singleSlashStart = regexp.MustCompile(`^/`)
	doubleSlashStart = regexp.MustCompile(`^//`)
	uriRegexp        = regexp.MustCompile(`^(([^:/?#]+?):)?(//([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?`)
)

// NewURI creates a new URI from components
func NewURI(scheme, authority, path, query, fragment string) *URI {
	uri := &URI{
		UriComponents: UriComponents{
			Scheme:    scheme,
			Authority: authority,
			Path:      path,
			Query:     query,
			Fragment:  fragment,
		},
	}
	validateURI(uri, false)
	return uri
}

// ParseURI parses a URI string into a URI object
func ParseURI(value string) *URI {
	matches := uriRegexp.FindStringSubmatch(value)
	if matches == nil {
		// If parsing fails, return an empty URI, consistent with TS implementation
		return &URI{UriComponents{Scheme: "", Authority: "", Path: "", Query: "", Fragment: ""}}
	}

	decodedPath, _ := url.PathUnescape(matches[5])
	decodedQuery, _ := url.QueryUnescape(matches[7])
	decodedFragment, _ := url.QueryUnescape(matches[9])

	uri := &URI{
		UriComponents: UriComponents{
			Scheme:    matches[2],
			Authority: matches[4],
			Path:      decodedPath,
			Query:     decodedQuery,
			Fragment:  decodedFragment,
		},
	}

	validateURI(uri, false)
	return uri
}

// File creates a file URI from a file path
func File(path string) *URI {
	scheme := "file"
	authority := ""

	// Normalize path separators for Windows
	if PlatformVar == Windows {
		path = strings.ReplaceAll(path, "\\", "/")
	}

	// Handle UNC paths (e.g., //server/share)
	if strings.HasPrefix(path, "//") {
		idx := strings.Index(path[2:], "/")
		if idx == -1 {
			authority = path[2:]
			path = "/"
		} else {
			authority = path[2 : idx+2]
			path = path[idx+2:]
			if path == "" {
				path = "/"
			}
		}
	}

	// Handle Windows drive letters
	if PlatformVar == Windows && len(path) >= 2 && path[1] == ':' {
		path = "/" + path
	}

	return NewURI(scheme, authority, path, "", "")
}

// From creates a URI from a UriComponents object
func From(components UriComponents) *URI {
	return NewURI(
		components.Scheme,
		components.Authority,
		components.Path,
		components.Query,
		components.Fragment,
	)
}

// validateURI validates a URI object
func validateURI(uri *URI, strict bool) {
	// scheme, must be set
	if uri.Scheme == "" && strict {
		panic(fmt.Sprintf("[UriError]: Scheme is missing: {scheme: \"\", authority: \"%s\", path: \"%s\", query: \"%s\", fragment: \"%s\"}",
			uri.Authority, uri.Path, uri.Query, uri.Fragment))
	}

	// scheme validation
	if uri.Scheme != "" && !schemePattern.MatchString(uri.Scheme) {
		panic("[UriError]: Scheme contains illegal characters.")
	}

	// path validation
	if uri.Path != "" {
		if uri.Authority != "" {
			if !singleSlashStart.MatchString(uri.Path) {
				panic("[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash (\"/\") character")
			}
		} else {
			if doubleSlashStart.MatchString(uri.Path) {
				panic("[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters (\"//\")")
			}
		}
	}
}

// schemeFix fixes the scheme for backward compatibility
func schemeFix(scheme string, strict bool) string {
	if scheme == "" && !strict {
		return "file"
	}
	return scheme
}

// referenceResolution implements a bit of RFC 3986 section 5
func referenceResolution(scheme, path string) string {
	switch scheme {
	case "https", "http", "file":
		if path == "" {
			path = "/"
		} else if !strings.HasPrefix(path, "/") {
			path = "/" + path
		}
	}
	return path
}

// ToString converts the URI to a string representation
func (uri *URI) ToString(skipEncoding ...bool) string {
	return uri.toString(len(skipEncoding) > 0 && skipEncoding[0])
}

func (uri *URI) toString(skipEncoding bool) string {
	var result strings.Builder

	if uri.Scheme != "" {
		result.WriteString(uri.Scheme)
		result.WriteString(":")
	}

	if uri.Authority != "" || uri.Scheme == "file" {
		result.WriteString("//")
	}

	if uri.Authority != "" {
		result.WriteString(uri.Authority)
	}

	if uri.Path != "" {
		if skipEncoding {
			result.WriteString(uri.Path)
		} else {
			result.WriteString(url.PathEscape(uri.Path))
		}
	}

	if uri.Query != "" {
		result.WriteString("?")
		if skipEncoding {
			result.WriteString(uri.Query)
		} else {
			result.WriteString(url.QueryEscape(uri.Query))
		}
	}

	if uri.Fragment != "" {
		result.WriteString("#")
		if skipEncoding {
			result.WriteString(uri.Fragment)
		} else {
			result.WriteString(url.QueryEscape(uri.Fragment))
		}
	}

	return result.String()
}

// ToJSON converts the URI to a JSON representation
func (uri *URI) ToJSON() UriComponents {
	return uri.UriComponents
}

// FSPath returns the file system path for file URIs
func (uri *URI) FSPath() string {
	if uri.Scheme != "file" {
		panic(fmt.Sprintf("cannot call fsPath on URI with scheme '%s'", uri.Scheme))
	}

	path := uri.Path

	// Handle Windows paths
	if PlatformVar == Windows {
		// Remove leading slash for Windows drive letters
		if len(path) >= 3 && path[0] == '/' && path[2] == ':' {
			path = path[1:]
		}
		// Convert forward slashes to backslashes
		path = strings.ReplaceAll(path, "/", "\\")
	}

	// URL decode the path
	if decoded, err := url.PathUnescape(path); err == nil {
		path = decoded
	}

	return path
}

// With creates a new URI with modified components
func (uri *URI) With(change UriComponents) *URI {
	scheme := uri.Scheme
	authority := uri.Authority
	path := uri.Path
	query := uri.Query
	fragment := uri.Fragment

	if change.Scheme != "" {
		scheme = change.Scheme
	}
	if change.Authority != "" {
		authority = change.Authority
	}
	if change.Path != "" {
		path = change.Path
	}
	if change.Query != "" {
		query = change.Query
	}
	if change.Fragment != "" {
		fragment = change.Fragment
	}

	if scheme == uri.Scheme &&
		authority == uri.Authority &&
		path == uri.Path &&
		query == uri.Query &&
		fragment == uri.Fragment {
		return uri
	}

	return NewURI(scheme, authority, path, query, fragment)
}

// IsUri checks if an object is a URI
func IsUri(thing interface{}) bool {
	_, ok := thing.(*URI)
	return ok
}

// Equals compares two URIs for equality
func (uri *URI) Equals(other *URI) bool {
	if other == nil {
		return false
	}

	return uri.Scheme == other.Scheme &&
		uri.Authority == other.Authority &&
		uri.Path == other.Path &&
		uri.Query == other.Query &&
		uri.Fragment == other.Fragment
}

// GetScheme returns the scheme component
func (uri *URI) GetScheme() string {
	return uri.Scheme
}

// GetAuthority returns the authority component
func (uri *URI) GetAuthority() string {
	return uri.Authority
}

// GetPath returns the path component
func (uri *URI) GetPath() string {
	return uri.Path
}

// GetQuery returns the query component
func (uri *URI) GetQuery() string {
	return uri.Query
}

// GetFragment returns the fragment component
func (uri *URI) GetFragment() string {
	return uri.Fragment
}

// String implements the Stringer interface
func (uri *URI) String() string {
	return uri.ToString()
}

// Clone creates a copy of the URI
func (uri *URI) Clone() *URI {
	return NewURI(uri.Scheme, uri.Authority, uri.Path, uri.Query, uri.Fragment)
}

// IsAbsolute checks if the URI is absolute (has a scheme)
func (uri *URI) IsAbsolute() bool {
	return uri.Scheme != ""
}

// IsFile checks if the URI is a file URI
func (uri *URI) IsFile() bool {
	return uri.Scheme == "file"
}

// IsHTTP checks if the URI is an HTTP URI
func (uri *URI) IsHTTP() bool {
	return uri.Scheme == "http" || uri.Scheme == "https"
}

// Resolve resolves a relative URI against this URI
func (uri *URI) Resolve(relative string) *URI {
	relativeURI := ParseURI(relative)

	// If relative URI has a scheme, it's absolute
	if relativeURI.Scheme != "" {
		return relativeURI
	}

	// Otherwise, resolve relative to this URI
	scheme := uri.Scheme
	authority := uri.Authority
	path := uri.Path
	query := relativeURI.Query
	fragment := relativeURI.Fragment

	if relativeURI.Authority != "" {
		authority = relativeURI.Authority
		path = relativeURI.Path
	} else {
		if relativeURI.Path == "" {
			if relativeURI.Query == "" {
				query = uri.Query
			}
		} else {
			if strings.HasPrefix(relativeURI.Path, "/") {
				path = relativeURI.Path
			} else {
				// Resolve relative path
				basePath := uri.Path
				if lastSlash := strings.LastIndex(basePath, "/"); lastSlash >= 0 {
					basePath = basePath[:lastSlash+1]
				} else {
					basePath = ""
				}
				path = basePath + relativeURI.Path
			}
		}
	}

	return NewURI(scheme, authority, path, query, fragment)
}

// GetFSPath returns the file system path for file URIs
func (u *URI) GetFSPath() string {
	if u.Scheme != "file" {
		return ""
	}
	return u.FSPath()
}

// Revive creates a URI from a UriComponents object (like JSON deserialization)
func Revive(components UriComponents) *URI {
	return From(components)
}

// FileURI creates a file URI from a file path (alias for File function)
func FileURI(path string) *URI {
	return File(path)
}

// Helper functions for UUID generation (simplified)
func randomInt32() uint32 { return 0x12345678 }
func randomInt16() uint16 { return 0x1234 }
func randomInt48() uint64 { return 0x123456789012 }
