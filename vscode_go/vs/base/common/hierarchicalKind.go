/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import "strings"

// HierarchicalKind represents a hierarchical kind with dot-separated values
type HierarchicalKind struct {
	Value string
}

const (
	// Sep is the separator used in hierarchical kinds
	HierarchicalKindSep = "."
)

var (
	// HierarchicalKindNone is a special kind that matches nothing
	HierarchicalKindNone = &HierarchicalKind{Value: "@@none@@"}
	// HierarchicalKindEmpty is an empty hierarchical kind
	HierarchicalKindEmpty = &HierarchicalKind{Value: ""}
)

// NewHierarchicalKind creates a new HierarchicalKind with the given value
func NewHierarchicalKind(value string) *HierarchicalKind {
	return &HierarchicalKind{Value: value}
}

// Equals checks if this HierarchicalKind equals another
func (h *HierarchicalKind) Equals(other *HierarchicalKind) bool {
	return h.Value == other.Value
}

// Contains checks if this HierarchicalKind contains another
func (h *HierarchicalKind) Contains(other *HierarchicalKind) bool {
	return h.Equals(other) || h.Value == "" || strings.HasPrefix(other.Value, h.Value+HierarchicalKindSep)
}

// Intersects checks if this HierarchicalKind intersects with another
func (h *HierarchicalKind) Intersects(other *HierarchicalKind) bool {
	return h.Contains(other) || other.Contains(h)
}

// Append creates a new HierarchicalKind by appending parts to this one
func (h *HierarchicalKind) Append(parts ...string) *HierarchicalKind {
	var allParts []string
	if h.Value != "" {
		allParts = append(allParts, h.Value)
	}
	allParts = append(allParts, parts...)
	return &HierarchicalKind{Value: strings.Join(allParts, HierarchicalKindSep)}
}
