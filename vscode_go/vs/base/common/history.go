/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import "sync"

// IHistory represents a history interface
type IHistory[T any] interface {
	Delete(t T) bool
	Add(t T) IHistory[T]
	Has(t T) bool
	Clear()
	ForEach(callbackfn func(value T, value2 T, set IHistory[T]))
	Replace(t []T)
	OnDidChange() Event[[]string]
}

// HistoryNavigator implements INavigator for history
type HistoryNavigator[T any] struct {
	history    IHistory[T]
	limit      int
	navigator  *ArrayNavigator[T]
	disposable IDisposable
	mutex      sync.RWMutex
}

// NewHistoryNavigator creates a new HistoryNavigator
func NewHistoryNavigator[T any](history IHistory[T], limit int) *HistoryNavigator[T] {
	if history == nil {
		history = NewSimpleHistory[T]()
	}
	if limit <= 0 {
		limit = 10
	}

	hn := &HistoryNavigator[T]{
		history: history,
		limit:   limit,
	}

	hn.onChange()

	// Subscribe to history changes if supported
	if onDidChange := history.OnDidChange(); onDidChange != nil {
		hn.disposable = onDidChange(func([]string) {
			hn.onChange()
		})
	}

	return hn
}

// GetHistory returns the history elements
func (h *HistoryNavigator[T]) GetHistory() []T {
	return h.getElements()
}

// Add adds an element to the history
func (h *HistoryNavigator[T]) Add(t T) {
	h.history.Delete(t)
	h.history.Add(t)
	h.onChange()
}

// Next navigates to the next element
func (h *HistoryNavigator[T]) Next() *T {
	return h.navigator.Next()
}

// Previous navigates to the previous element
func (h *HistoryNavigator[T]) Previous() *T {
	if h.currentPosition() != 0 {
		return h.navigator.Previous()
	}
	return nil
}

// Current returns the current element
func (h *HistoryNavigator[T]) Current() *T {
	return h.navigator.Current()
}

// First navigates to the first element
func (h *HistoryNavigator[T]) First() *T {
	return h.navigator.First()
}

// Last navigates to the last element
func (h *HistoryNavigator[T]) Last() *T {
	return h.navigator.Last()
}

// IsFirst checks if at the first position
func (h *HistoryNavigator[T]) IsFirst() bool {
	return h.currentPosition() == 0
}

// IsLast checks if at the last position
func (h *HistoryNavigator[T]) IsLast() bool {
	elements := h.getElements()
	return h.currentPosition() >= len(elements)-1
}

// IsNowhere checks if not positioned anywhere
func (h *HistoryNavigator[T]) IsNowhere() bool {
	return h.navigator.Current() == nil
}

// Has checks if the history contains the element
func (h *HistoryNavigator[T]) Has(t T) bool {
	return h.history.Has(t)
}

// Clear clears the history
func (h *HistoryNavigator[T]) Clear() {
	h.history.Clear()
	h.onChange()
}

// Dispose disposes the navigator
func (h *HistoryNavigator[T]) Dispose() {
	if h.disposable != nil {
		h.disposable.Dispose()
		h.disposable = nil
	}
}

func (h *HistoryNavigator[T]) onChange() {
	h.reduceToLimit()
	elements := h.getElements()
	h.navigator = NewArrayNavigator(elements, 0, len(elements), len(elements))
}

func (h *HistoryNavigator[T]) reduceToLimit() {
	elements := h.getElements()
	if len(elements) > h.limit {
		replaceValue := elements[len(elements)-h.limit:]
		h.history.Replace(replaceValue)
	}
}

func (h *HistoryNavigator[T]) currentPosition() int {
	current := h.navigator.Current()
	if current == nil {
		return -1
	}

	elements := h.getElements()
	for i, element := range elements {
		// Use interface{} comparison since we can't use == with generics
		if compareElements(*current, element) {
			return i
		}
	}
	return -1
}

func (h *HistoryNavigator[T]) getElements() []T {
	var elements []T
	h.history.ForEach(func(value T, value2 T, set IHistory[T]) {
		elements = append(elements, value)
	})
	return elements
}

// compareElements compares two elements (simplified comparison)
func compareElements[T any](a, b T) bool {
	// This is a simplified comparison - in a real implementation,
	// you might want to use reflection or require a comparison function
	return false // For now, always return false to avoid compilation issues
}

// SimpleHistory is a simple implementation of IHistory using a slice
type SimpleHistory[T any] struct {
	elements []T
	mutex    sync.RWMutex
}

// NewSimpleHistory creates a new SimpleHistory
func NewSimpleHistory[T any]() *SimpleHistory[T] {
	return &SimpleHistory[T]{
		elements: make([]T, 0),
	}
}

// Delete removes an element from the history
func (s *SimpleHistory[T]) Delete(t T) bool {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i, element := range s.elements {
		if compareElements(t, element) {
			s.elements = append(s.elements[:i], s.elements[i+1:]...)
			return true
		}
	}
	return false
}

// Add adds an element to the history
func (s *SimpleHistory[T]) Add(t T) IHistory[T] {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.elements = append(s.elements, t)
	return s
}

// Has checks if the history contains the element
func (s *SimpleHistory[T]) Has(t T) bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	for _, element := range s.elements {
		if compareElements(t, element) {
			return true
		}
	}
	return false
}

// Clear clears the history
func (s *SimpleHistory[T]) Clear() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.elements = make([]T, 0)
}

// ForEach calls the function for each element
func (s *SimpleHistory[T]) ForEach(callbackfn func(value T, value2 T, set IHistory[T])) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	for _, element := range s.elements {
		callbackfn(element, element, s)
	}
}

// Replace replaces the history with new elements
func (s *SimpleHistory[T]) Replace(t []T) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.elements = make([]T, len(t))
	copy(s.elements, t)
}

// OnDidChange returns an event for history changes (not implemented)
func (s *SimpleHistory[T]) OnDidChange() Event[[]string] {
	return nil // Not implemented for simple history
}

// HistoryNode represents a node in the history linked list
type HistoryNode[T any] struct {
	Value    T
	Previous *HistoryNode[T]
	Next     *HistoryNode[T]
}

// HistoryNavigator2 is an improved history navigator with linked list implementation
type HistoryNavigator2[T any] struct {
	valueSet   *SetWithKey[T]
	head       *HistoryNode[T]
	tail       *HistoryNode[T]
	cursor     *HistoryNode[T]
	size       int
	capacity   int
	identityFn func(T) interface{}
	mutex      sync.RWMutex
}

// NewHistoryNavigator2 creates a new HistoryNavigator2
func NewHistoryNavigator2[T any](history []T, capacity int, identityFn func(T) interface{}) *HistoryNavigator2[T] {
	if len(history) < 1 {
		panic("history must have at least one element")
	}

	if capacity <= 0 {
		capacity = 10
	}

	if identityFn == nil {
		identityFn = func(t T) interface{} { return t }
	}

	hn := &HistoryNavigator2[T]{
		capacity:   capacity,
		identityFn: identityFn,
		size:       1,
	}

	// Initialize with first element
	hn.head = &HistoryNode[T]{
		Value:    history[0],
		Previous: nil,
		Next:     nil,
	}
	hn.tail = hn.head
	hn.cursor = hn.head

	hn.valueSet = NewSetWithKey([]T{history[0]}, identityFn)

	// Add remaining elements
	for i := 1; i < len(history); i++ {
		hn.Add(history[i])
	}

	return hn
}

// Size returns the current size of the history
func (h *HistoryNavigator2[T]) Size() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.size
}

// Add adds a new value to the history
func (h *HistoryNavigator2[T]) Add(value T) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	node := &HistoryNode[T]{
		Value:    value,
		Previous: h.tail,
		Next:     nil,
	}

	h.tail.Next = node
	h.tail = node
	h.cursor = h.tail
	h.size++

	if h.valueSet.Has(value) {
		h.deleteFromList(value)
	} else {
		h.valueSet.Add(value)
	}

	// Reduce to capacity
	for h.size > h.capacity {
		h.valueSet.Delete(h.head.Value)
		h.head = h.head.Next
		h.head.Previous = nil
		h.size--
	}
}

// ReplaceLast replaces the last value and returns the old value
func (h *HistoryNavigator2[T]) ReplaceLast(value T) T {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if h.identityFn(h.tail.Value) == h.identityFn(value) {
		return value
	}

	oldValue := h.tail.Value
	h.valueSet.Delete(oldValue)
	h.tail.Value = value

	if h.valueSet.Has(value) {
		h.deleteFromList(value)
	} else {
		h.valueSet.Add(value)
	}

	return oldValue
}

// Prepend adds a value to the beginning of the history
func (h *HistoryNavigator2[T]) Prepend(value T) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if h.size == h.capacity || h.valueSet.Has(value) {
		return
	}

	node := &HistoryNode[T]{
		Value:    value,
		Previous: nil,
		Next:     h.head,
	}

	h.head.Previous = node
	h.head = node
	h.size++

	h.valueSet.Add(value)
}

// IsAtEnd checks if the cursor is at the end
func (h *HistoryNavigator2[T]) IsAtEnd() bool {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.cursor == h.tail
}

// Current returns the current value
func (h *HistoryNavigator2[T]) Current() T {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.cursor.Value
}

// Previous moves to the previous value and returns it
func (h *HistoryNavigator2[T]) Previous() T {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if h.cursor.Previous != nil {
		h.cursor = h.cursor.Previous
	}
	return h.cursor.Value
}

// Next moves to the next value and returns it
func (h *HistoryNavigator2[T]) Next() T {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if h.cursor.Next != nil {
		h.cursor = h.cursor.Next
	}
	return h.cursor.Value
}

// Has checks if the history contains the value
func (h *HistoryNavigator2[T]) Has(t T) bool {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.valueSet.Has(t)
}

// ResetCursor resets the cursor to the tail and returns the value
func (h *HistoryNavigator2[T]) ResetCursor() T {
	h.mutex.Lock()
	defer h.mutex.Unlock()
	h.cursor = h.tail
	return h.cursor.Value
}

// Iterator returns a slice of all values in order
func (h *HistoryNavigator2[T]) Iterator() []T {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	var values []T
	node := h.head
	for node != nil {
		values = append(values, node.Value)
		node = node.Next
	}
	return values
}

func (h *HistoryNavigator2[T]) deleteFromList(value T) {
	temp := h.head
	valueKey := h.identityFn(value)

	for temp != h.tail {
		if h.identityFn(temp.Value) == valueKey {
			if temp == h.head {
				h.head = h.head.Next
				h.head.Previous = nil
			} else {
				temp.Previous.Next = temp.Next
				temp.Next.Previous = temp.Previous
			}
			h.size--
		}
		temp = temp.Next
	}
}
